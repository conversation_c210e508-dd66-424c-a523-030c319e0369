Enter your choice: 1
Starting Netbird installation...
Installing Netbird...
====================
Checking Netbird installation prerequisites...
Prerequisites check passed.

Netbird requires a domain name for proper operation.
The domain should point to this server's public IP address.
Example: netbird.yourdomain.com

Enter your domain name: netbird.fazee.pw
Checking domain accessibility...
Using domain: netbird.fazee.pw

Checking for existing SSL certificates...
Checking for existing SSL certificates...
Domain: netbird.fazee.pw
Base domain: fazee.pw
Found certificates for domain: fazee.pw
Validating certificate files for domain: fazee.pw
All required certificate files found.

Found existing SSL certificates in cloud storage.
Certificate domain: fazee.pw
Target domain: netbird.fazee.pw
Note: Using wildcard certificate from base domain.
Would you like to use these existing certificates? (y/n): y
Setting up SSL certificates for Netbird...
Downloading certificates for domain: fazee.pw
Downloading from: Eyunion:Backups/Certificates/fazee.pw
Target directory: /opt/netbird/ssl
Transferred:        6.487 KiB / 6.487 KiB, 100%, 6.246 KiB/s, ETA 0s
Transferred:            5 / 5, 100%
Elapsed time:         2.9s
Certificates downloaded successfully.
SSL certificates downloaded successfully.
Certificate files location: /opt/netbird/ssl
Creating SSL override configuration...
SSL override configuration created.
Custom SSL certificates configured successfully.
Netbird installation will use these certificates and skip Let's Encrypt generation.
Custom SSL certificates configured successfully.
Creating installation directory at /opt/netbird...
Downloading and running Netbird installation script...
This may take several minutes...
Custom SSL certificates detected. Using custom installation process...
Installing Netbird with custom SSL certificates...
Domain: netbird.fazee.pw
Installation path: /opt/netbird
Using custom SSL certificates from: fazee.pw
Downloading original Netbird installation script for configuration extraction...
Creating custom installation script...
sed: -e expression #1, char 20: unknown option to `s'
Custom installation script created successfully.
Running custom Netbird installation...
Starting custom Netbird installation...
Domain: netbird.fazee.pw
Installation path: /opt/netbird
Downloading Netbird configuration files...
Configuration files created successfully.
Starting Netbird services...
WARN[0000] /opt/netbird/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion 
[+] Running 54/54
 ✔ caddy Pulled                                                                                                                                                                                                                         3.7s 
 ✔ zitadel Pulled                                                                                                                                                                                                                      22.7s 
 ✔ zdb Pulled                                                                                                                                                                                                                          23.1s 
 ✔ management Pulled                                                                                                                                                                                                                   23.4s 
 ✔ signal Pulled                                                                                                                                                                                                                        9.6s 
 ✔ dashboard Pulled                                                                                                                                                                                                                    24.1s 
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                             
                                                                                                                                                                                                                                        [+] Running 10/10
 ✔ Network netbird_netbird         Created                                                                                                                                                                                              0.1s 
 ✔ Volume "netbird_zitadel_data"   Created                                                                                                                                                                                              0.0s 
 ✔ Volume "netbird_caddy_data"     Created                                                                                                                                                                                              0.0s 
 ✔ Volume "netbird_caddy_config"   Created                                                                                                                                                                                              0.0s 
 ✔ Container netbird-signal-1      Started                                                                                                                                                                                              1.1s 
 ✔ Container netbird-dashboard-1   Started                                                                                                                                                                                              1.1s 
 ✔ Container netbird-caddy-1       Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-zdb-1         Healthy                                                                                                                                                                                             11.7s 
 ✔ Container netbird-management-1  Started                                                                                                                                                                                              2.4s 
 ✔ Container netbird-zitadel-1     Started                                                                                                                                                                                             11.9s 
Waiting for services to start...
Netbird installation with custom SSL completed!
Access your installation at: https://netbird.fazee.pw
Custom SSL Netbird installation completed successfully!
Configuring Netbird to use custom SSL certificates...
Updating Caddyfile to use custom SSL certificates...
Caddyfile updated successfully.
Updating docker-compose.yml to mount SSL certificates...
Docker compose configuration updated.
Netbird installation with custom SSL completed successfully!
Netbird installation completed successfully.


Netbird Status: ✓ Installed & Running | Domain: netbird.fazee.pw


root@remote:~# docker ps
CONTAINER ID   IMAGE                            COMMAND                  CREATED          STATUS                          PORTS                                                                                                                                              NAMES
632d302d936e   ghcr.io/zitadel/zitadel:latest   "/app/zitadel start-…"   49 seconds ago   Restarting (1) 8 seconds ago                                                                                                                                                       netbird-zitadel-1
7ffe64d58d32   netbirdio/management:latest      "/go/bin/netbird-mgm…"   49 seconds ago   Restarting (1) 13 seconds ago                                                                                                                                                      netbird-management-1
9eb3cc5e8be8   postgres:15-alpine               "docker-entrypoint.s…"   50 seconds ago   Up 48 seconds (healthy)         5432/tcp                                                                                                                                           netbird-zdb-1
8c2c43402260   netbirdio/signal:latest          "/go/bin/netbird-sig…"   50 seconds ago   Up 48 seconds                                                                                                                                                                      netbird-signal-1
497205822ac3   caddy:2-alpine                   "caddy run --config …"   50 seconds ago   Up 48 seconds                   0.0.0.0:80->80/tcp, [::]:80->80/tcp, 0.0.0.0:443->443/tcp, [::]:443->443/tcp, 0.0.0.0:33073->33073/tcp, [::]:33073->33073/tcp, 443/udp, 2019/tcp   netbird-caddy-1
43487762f88e   netbirdio/dashboard:latest       "/usr/bin/supervisor…"   50 seconds ago   Up 48 seconds                   80/tcp, 443/tcp                                                                                                                                    netbird-dashboard-1
root@remote:~# docker logs netbird-management-1
2025-06-10T16:25:09Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:09Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:09Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:09Z INFO management/server/migration/migration.go:128: Table for peer.Peer does not exist, no migration needed
2025-06-10T16:25:09Z INFO management/server/migration/migration.go:128: Table for peer.Peer does not exist, no migration needed
2025-06-10T16:25:09Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:09Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:11Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:11Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:11Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:11Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:12Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:12Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:12Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:12Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:13Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:13Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:13Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:13Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:15Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:15Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:15Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:15Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:17Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:17Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:17Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:17Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:21Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:21Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:21Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:21Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:28Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:28Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:28Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:28Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/telemetry/app_metrics.go:193: enabled application metrics and exposing on http://0.0.0.0:9090
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/store/store.go:256: using SQLite store engine
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/store/sql_store.go:89: Set max open db connections to 1
2025-06-10T16:25:42Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:42Z INFO management/server/migration/migration.go:142: No records in table peers, no migration needed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:257: No plain setup keys found in table setup_keys, no migration needed
2025-06-10T16:25:42Z INFO management/server/migration/migration.go:295: Migration of plain setup key to hashed setup key completed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_resources, no migration needed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_resources completed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enabled found in table network_routers, no migration needed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enabled to default value in table network_routers completed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/activity/store/sql_store.go:260: using sqlite as activity event store engine
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty name found in table deleted_users, no migration needed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty name to default value in table deleted_users completed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:338: No rows with empty enc_algo found in table deleted_users, no migration needed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/server/migration/migration.go:352: Migration of empty enc_algo to default value in table deleted_users completed
2025-06-10T16:25:42Z INFO [context: SYSTEM] management/cmd/management.go:185: update config with activity store key
Error: failed to write out store encryption key: open /etc/netbird/management.json: read-only file system
root@remote:~# docker logs netbird-zitadel-1
time="2025-06-10T16:25:19Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:19Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:19Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:19Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:19Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:20Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:20Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:20Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:20Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:20Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:20Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:20Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=14_events_push
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=40_init_push_func_v4
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/40.go:51" file=01_type.sql migration=40_init_push_func_v4
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/40.go:51" file=02_func.sql migration=40_init_push_func_v4
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=01_tables
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=02_assets
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=28_add_search_table
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/46.go:29" file=01-role_permissions_view.sql migration=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/46.go:29" file=02-instance_orgs_view.sql migration=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/46.go:29" file=03-instance_members_view.sql migration=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/46.go:29" file=04-org_members_view.sql migration=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/46.go:29" file=05-project_members_view.sql migration=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="execute statement" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/46.go:29" file=06-permitted_orgs_function.sql migration=46_init_permission_functions
time="2025-06-10T16:25:20Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:20Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:20Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:20Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:20Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:22Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:22Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:22Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:22Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:22Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:22Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:22Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:22Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:22Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:22Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:22Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:22Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:22Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:22Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:22Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:23Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:23Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:23Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:23Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:23Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:23Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:23Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:23Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:23Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:24Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:24Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:24Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:24Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:24Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:24Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:24Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:24Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:24Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:24Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:24Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:24Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:24Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:24Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:24Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:24Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:24Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:26Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:26Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:26Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:26Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:26Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:26Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:26Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:26Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:26Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:26Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:26Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:27Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:27Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:27Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:27Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:27Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:27Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:29Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:30Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:30Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:30Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:30Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:30Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:30Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:30Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:30Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:30Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:30Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:30Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:30Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:30Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:30Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:30Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:30Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:33Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:33Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:33Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:33Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:33Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:33Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:33Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:33Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:33Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:33Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:33Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:33Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:33Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:33Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:33Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:33Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:33Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:38Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:38Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:38Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:38Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:38Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:38Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:38Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:38Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:38Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:38Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:38Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:38Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:38Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:38Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:38Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:38Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:38Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:25:46Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:25:46Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:25:46Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:25:46Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:25:46Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:25:46Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:25:46Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:25:46Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:25:46Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:25:46Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:25:46Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:25:47Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:25:47Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:25:47Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:25:47Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:25:47Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:25:47Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
time="2025-06-10T16:26:01Z" level=info msg="initialization started" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/init.go:70"
time="2025-06-10T16:26:01Z" level=info msg="verify user" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_user.go:40" username=zitadel
time="2025-06-10T16:26:01Z" level=info msg="verify database" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_database.go:40" database=zitadel
time="2025-06-10T16:26:01Z" level=info msg="verify grant" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_grant.go:35" database=zitadel user=zitadel
time="2025-06-10T16:26:01Z" level=info msg="verify zitadel" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:80" database=zitadel
time="2025-06-10T16:26:01Z" level=info msg="verify system" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:46"
time="2025-06-10T16:26:01Z" level=info msg="verify encryption keys" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:51"
time="2025-06-10T16:26:01Z" level=info msg="verify projections" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:56"
time="2025-06-10T16:26:01Z" level=info msg="verify eventstore" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:61"
time="2025-06-10T16:26:01Z" level=info msg="verify events tables" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:66"
time="2025-06-10T16:26:01Z" level=info msg="verify unique constraints" caller="/home/<USER>/work/zitadel/zitadel/cmd/initialise/verify_zitadel.go:71"
time="2025-06-10T16:26:01Z" level=info msg="setup started" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:108"
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=14_events_push
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=40_init_push_func_v4
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=01_tables
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=02_assets
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=28_add_search_table
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=31_add_aggregate_index_to_fields
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=46_init_permission_functions
time="2025-06-10T16:26:01Z" level=info msg="verify migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:43" name=03_default_instance
time="2025-06-10T16:26:01Z" level=info msg="starting migration" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:66" name=03_default_instance
time="2025-06-10T16:26:01Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/internal/migration/migration.go:68" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instance
time="2025-06-10T16:26:01Z" level=error msg="migration failed" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:353" error="cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26" name=03_default_instancetime="2025-06-10T16:26:01Z" level=fatal msg="setup failed, skipping cleanup" caller="/home/<USER>/work/zitadel/zitadel/cmd/setup/setup.go:124" error="migration failed: cannot start key storage: ID= Message=masterkey must be 32 bytes, but is 26"
root@remote:~# 