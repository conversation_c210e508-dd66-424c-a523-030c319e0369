# Netbird SSL Certificate Management

This module provides functionality to use existing wildcard SSL certificates from cloud storage instead of generating new Let's Encrypt certificates during Netbird installation.

## Features

- **Automatic Certificate Detection**: Checks cloud storage for existing wildcard certificates
- **Base Domain Matching**: Automatically matches subdomains to wildcard certificates
- **No Certificate Generation**: Prevents Let's Encrypt certificate generation to avoid rate limits
- **Cloud Storage Integration**: Uses rclone to access certificates from cloud storage
- **Custom Installation Process**: Modified installation that uses existing certificates

## Prerequisites

1. **Rclone Configuration**: Ensure rclone is configured with access to your certificate storage
2. **Certificate Storage Structure**: Certificates should be stored in the following structure:
   ```
   Eyunion:Backups/Certificates/
   ├── domain1.com/
   │   ├── fullchain.pem
   │   ├── privkey.pem
   │   ├── cert.pem
   │   ├── chain.pem
   │   └── README
   └── domain2.com/
       ├── fullchain.pem
       ├── privkey.pem
       ├── cert.pem
       ├── chain.pem
       └── README
   ```

## How It Works

### During Installation

1. **Domain Input**: User enters the Netbird domain (e.g., `netbird.example.com`)
2. **Certificate Check**: System checks cloud storage for certificates:
   - First checks for exact domain match (`netbird.example.com`)
   - Then checks for base domain wildcard certificate (`example.com`)
3. **Certificate Download**: If found, downloads certificates to `/opt/netbird/ssl/`
4. **Custom Installation**: Uses modified installation process that:
   - Skips Let's Encrypt certificate generation
   - Configures Caddy to use custom certificates
   - Mounts SSL directory in Docker containers

### Certificate Validation

The system validates that required certificate files exist:
- `fullchain.pem` (required)
- `privkey.pem` (required)
- `cert.pem` (optional)
- `chain.pem` (optional)

## Usage

### Installation with Custom SSL

1. Run Netbird installation from the menu
2. Enter your domain name when prompted
3. System will automatically check for existing certificates
4. If found, choose "Yes" to use existing certificates
5. Installation proceeds with custom SSL configuration

### SSL Management Menu

Access via: `Netbird Menu > SSL Certificate Management`

Available options:
1. **List Available Certificates**: Shows all certificate domains in cloud storage
2. **Setup Custom SSL**: Configure SSL for existing Netbird installation
3. **Remove Custom SSL**: Revert to default Netbird certificates
4. **Test Functionality**: Verify SSL certificate system is working
5. **Return to Main Menu**

## Configuration Files

### SSL Override Environment
Location: `/opt/netbird/.ssl_override.env`
```bash
NETBIRD_DISABLE_LETSENCRYPT=true
NETBIRD_CUSTOM_SSL=true
NETBIRD_SSL_CERT_PATH=/ssl/fullchain.pem
NETBIRD_SSL_KEY_PATH=/ssl/privkey.pem
NETBIRD_DOMAIN=netbird.example.com
NETBIRD_CERT_DOMAIN=example.com
```

### Custom Caddyfile
The system creates a custom Caddyfile that uses your certificates:
```
{
    auto_https off
}

netbird.example.com {
    tls /ssl/fullchain.pem /ssl/privkey.pem
    
    # Management API
    handle /api/* {
        reverse_proxy management:80
    }
    
    # Signal server
    handle /signalexchange.SignalExchange/* {
        reverse_proxy h2c://signal:80
    }
    
    # Dashboard
    handle /* {
        reverse_proxy dashboard:80
    }
}

# Signal gRPC endpoint
netbird.example.com:33073 {
    tls /ssl/fullchain.pem /ssl/privkey.pem
    reverse_proxy h2c://signal:80
}
```

## Troubleshooting

### Common Issues

1. **Rclone Not Configured**
   - Error: "rclone remote 'Eyunion' is not configured"
   - Solution: Configure rclone with `rclone config`

2. **Certificates Not Found**
   - Check certificate storage structure
   - Verify domain name spelling
   - Ensure base domain certificates exist for wildcard support

3. **Permission Issues**
   - Ensure proper file permissions on certificate files
   - Check Docker container can access mounted SSL directory

### Testing

Use the "Test SSL Certificate Functionality" option to verify:
- Rclone connectivity
- Certificate listing capability
- Domain extraction logic

## Benefits

- **Avoid Rate Limits**: No new certificate generation
- **Wildcard Support**: One certificate covers multiple subdomains
- **Centralized Management**: Certificates stored in cloud storage
- **Automatic Detection**: Smart matching of domains to certificates
- **No Manual Configuration**: Automated setup process

## Security Notes

- Certificates are downloaded with read-only permissions
- SSL directory is mounted read-only in containers
- Original Netbird configuration files are backed up before modification
- Custom SSL configuration can be easily reverted
