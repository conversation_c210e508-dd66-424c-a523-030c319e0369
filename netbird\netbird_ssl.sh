#!/bin/bash

# Netbird SSL Certificate Management Functions

# Color constants for output formatting
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default rclone remote (can be overridden)
RCLONE_REMOTE="${RCLONE_REMOTE:-Eyunion}"

# Function to extract base domain from a full domain
extract_base_domain() {
    local full_domain="$1"
    
    # Remove subdomain to get base domain (e.g., netbird.example.com -> example.com)
    # This handles cases like: subdomain.domain.com -> domain.com
    echo "$full_domain" | sed -E 's/^[^.]+\.(.+)$/\1/'
}

# Function to check if rclone is available and configured
check_rclone_availability() {
    if ! command -v rclone >/dev/null 2>&1; then
        echo -e "${RED}Error: rclone is not installed or not in PATH.${NC}"
        echo "Please install rclone first to use custom SSL certificates."
        return 1
    fi
    
    # Check if the remote is configured
    if ! rclone listremotes | grep -q "^${RCLONE_REMOTE}:$"; then
        echo -e "${RED}Error: rclone remote '${RCLONE_REMOTE}' is not configured.${NC}"
        echo "Please configure your rclone remote first."
        return 1
    fi
    
    return 0
}

# Function to list available certificate domains in cloud storage
list_available_certificate_domains() {
    echo -e "${BLUE}Checking available certificate domains in cloud storage...${NC}"
    
    if ! check_rclone_availability; then
        return 1
    fi
    
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates"
    local domains=$(rclone lsd "$cert_path" 2>/dev/null | awk '{print $5}' | grep -v '^$')
    
    if [ -z "$domains" ]; then
        echo -e "${YELLOW}No certificate domains found in cloud storage.${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Available certificate domains:${NC}"
    echo "$domains" | while read -r domain; do
        echo "  - $domain"
    done
    
    return 0
}

# Function to check if certificates exist for a domain (including wildcard)
check_certificate_exists_in_cloud() {
    local domain="$1"
    local base_domain=$(extract_base_domain "$domain")
    
    if ! check_rclone_availability; then
        return 1
    fi
    
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates"
    
    # Check for exact domain match first
    if rclone lsd "$cert_path" 2>/dev/null | grep -q " $domain$"; then
        echo "$domain"
        return 0
    fi
    
    # Check for base domain (wildcard certificates)
    if [ "$domain" != "$base_domain" ] && rclone lsd "$cert_path" 2>/dev/null | grep -q " $base_domain$"; then
        echo "$base_domain"
        return 0
    fi
    
    return 1
}

# Function to validate certificate files in cloud storage
validate_certificate_files() {
    local cert_domain="$1"
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates/$cert_domain"
    
    echo -e "${BLUE}Validating certificate files for domain: $cert_domain${NC}"
    
    # Required certificate files
    local required_files=("fullchain.pem" "privkey.pem")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if ! rclone ls "$cert_path" 2>/dev/null | grep -q " $file$"; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}Missing required certificate files:${NC}"
        printf '%s\n' "${missing_files[@]}"
        return 1
    fi
    
    echo -e "${GREEN}All required certificate files found.${NC}"
    return 0
}

# Function to download certificates from cloud storage
download_certificates() {
    local cert_domain="$1"
    local target_dir="$2"
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates/$cert_domain"
    
    echo -e "${BLUE}Downloading certificates for domain: $cert_domain${NC}"
    
    # Create target directory
    sudo mkdir -p "$target_dir"
    
    # Download certificate files
    echo "Downloading from: $cert_path"
    echo "Target directory: $target_dir"
    
    if rclone copy "$cert_path" "$target_dir" --progress; then
        echo -e "${GREEN}Certificates downloaded successfully.${NC}"
        
        # Set proper permissions
        sudo chmod 644 "$target_dir"/*.pem 2>/dev/null || true
        sudo chown root:root "$target_dir"/*.pem 2>/dev/null || true
        
        return 0
    else
        echo -e "${RED}Failed to download certificates.${NC}"
        return 1
    fi
}

# Function to setup SSL certificates for Netbird
setup_netbird_ssl_certificates() {
    local domain="$1"
    local netbird_path="$2"
    local cert_domain="$3"

    echo -e "${BLUE}Setting up SSL certificates for Netbird...${NC}"

    # Create SSL directory in Netbird installation
    local ssl_dir="$netbird_path/ssl"
    sudo mkdir -p "$ssl_dir"

    # Download certificates to SSL directory
    if download_certificates "$cert_domain" "$ssl_dir"; then
        echo -e "${GREEN}SSL certificates downloaded successfully.${NC}"
        echo "Certificate files location: $ssl_dir"

        # Create a marker file to indicate custom SSL is used
        echo "$cert_domain" | sudo tee "$ssl_dir/.custom_ssl_domain" > /dev/null

        return 0
    else
        echo -e "${RED}Failed to setup SSL certificates.${NC}"
        return 1
    fi
}

# Function to configure Netbird to use custom SSL certificates (post-installation)
configure_netbird_custom_ssl() {
    local domain="$1"
    local netbird_path="$2"
    local cert_domain="$3"

    echo -e "${BLUE}Configuring Netbird to use custom SSL certificates...${NC}"

    local ssl_dir="$netbird_path/ssl"
    local caddyfile="$netbird_path/Caddyfile"

    # Check if Caddyfile exists
    if [ ! -f "$caddyfile" ]; then
        echo -e "${RED}Caddyfile not found at $caddyfile${NC}"
        return 1
    fi

    # Backup original Caddyfile
    sudo cp "$caddyfile" "$caddyfile.backup.$(date +%Y%m%d_%H%M%S)"

    # Create new Caddyfile with custom SSL configuration
    echo -e "${BLUE}Updating Caddyfile to use custom SSL certificates...${NC}"

    # Create a new Caddyfile that uses custom certificates
    cat << EOF | sudo tee "$caddyfile" > /dev/null
# Netbird Caddyfile with custom SSL certificates
# Generated by Netbird SSL management module

$domain {
    tls /ssl/fullchain.pem /ssl/privkey.pem

    # Management API
    handle /api/* {
        reverse_proxy management:80
    }

    # Signal server
    handle /signalexchange.SignalExchange/* {
        reverse_proxy h2c://signal:80
    }

    # Dashboard
    handle /* {
        reverse_proxy dashboard:80
    }

    # Enable logging
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# Signal gRPC endpoint
$domain:33073 {
    tls /ssl/fullchain.pem /ssl/privkey.pem
    reverse_proxy h2c://signal:80
}
EOF

    echo -e "${GREEN}Caddyfile updated successfully.${NC}"

    # Update docker-compose.yml to mount SSL certificates
    local compose_file="$netbird_path/docker-compose.yml"
    if [ -f "$compose_file" ]; then
        echo -e "${BLUE}Updating docker-compose.yml to mount SSL certificates...${NC}"

        # Backup original docker-compose.yml
        sudo cp "$compose_file" "$compose_file.backup.$(date +%Y%m%d_%H%M%S)"

        # Add SSL volume mount to caddy service
        if grep -q "caddy:" "$compose_file"; then
            # Use sed to add SSL volume mount to caddy service
            sudo sed -i '/caddy:/,/^[[:space:]]*[^[:space:]]/ {
                /volumes:/a\
      - ./ssl:/ssl:ro
            }' "$compose_file"

            echo -e "${GREEN}Docker compose configuration updated.${NC}"
        else
            echo -e "${YELLOW}Warning: Could not find caddy service in docker-compose.yml${NC}"
        fi
    else
        echo -e "${YELLOW}Warning: docker-compose.yml not found${NC}"
    fi

    return 0
}

# Function to apply SSL configuration after Netbird installation
apply_pending_ssl_configuration() {
    local netbird_path="$1"

    if [ ! -f "$netbird_path/.pending_ssl_config" ]; then
        return 0  # No pending SSL configuration
    fi

    echo -e "${BLUE}Applying custom SSL certificate configuration...${NC}"

    local ssl_config=$(cat "$netbird_path/.pending_ssl_config")
    local domain=$(echo "$ssl_config" | cut -d'|' -f1)
    local cert_domain=$(echo "$ssl_config" | cut -d'|' -f2)

    echo "Domain: $domain"
    echo "Certificate domain: $cert_domain"

    # Configure Netbird to use custom SSL certificates
    if configure_netbird_custom_ssl "$domain" "$netbird_path" "$cert_domain"; then
        echo -e "${GREEN}Custom SSL configuration applied successfully.${NC}"

        # Remove pending configuration file
        sudo rm -f "$netbird_path/.pending_ssl_config"

        # Create final marker file
        echo "$cert_domain" | sudo tee "$netbird_path/ssl/.custom_ssl_domain" > /dev/null

        echo -e "${BLUE}Restarting Netbird services to apply SSL changes...${NC}"
        cd "$netbird_path"
        docker compose down
        sleep 3
        docker compose up -d

        echo -e "${GREEN}Netbird is now using your custom SSL certificates!${NC}"
        echo "Certificate domain: $cert_domain"
        echo "Target domain: $domain"

        return 0
    else
        echo -e "${RED}Failed to apply custom SSL configuration.${NC}"
        return 1
    fi
}

# Main function to check and setup SSL certificates
check_and_setup_ssl_certificates() {
    local domain="$1"
    local netbird_path="${2:-/opt/netbird}"
    
    echo -e "${BLUE}Checking for existing SSL certificates...${NC}"
    echo "Domain: $domain"
    echo "Base domain: $(extract_base_domain "$domain")"
    
    # Check if certificates exist in cloud storage
    local cert_domain
    if cert_domain=$(check_certificate_exists_in_cloud "$domain"); then
        echo -e "${GREEN}Found certificates for domain: $cert_domain${NC}"
        
        # Validate certificate files
        if validate_certificate_files "$cert_domain"; then
            # Ask user if they want to use existing certificates
            echo ""
            echo -e "${YELLOW}Found existing SSL certificates in cloud storage.${NC}"
            echo "Certificate domain: $cert_domain"
            echo "Target domain: $domain"
            
            if [ "$cert_domain" != "$domain" ]; then
                echo -e "${BLUE}Note: Using wildcard certificate from base domain.${NC}"
            fi
            
            read -rp "Would you like to use these existing certificates? (y/n): " use_existing
            if [[ "$use_existing" =~ ^[Yy]$ ]]; then
                if setup_netbird_ssl_certificates "$domain" "$netbird_path" "$cert_domain"; then
                    # Store certificate info for post-installation configuration
                    echo "$domain|$cert_domain" | sudo tee "$netbird_path/.pending_ssl_config" > /dev/null
                    return 0
                else
                    echo -e "${RED}Failed to setup existing certificates.${NC}"
                    return 1
                fi
            else
                echo "Skipping existing certificates. New certificates will be generated."
                return 1
            fi
        else
            echo -e "${RED}Certificate validation failed.${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}No existing certificates found for domain: $domain${NC}"
        echo -e "${YELLOW}Base domain $(extract_base_domain "$domain") also not found.${NC}"
        return 1
    fi
}

# Function to manage SSL certificates (menu option)
manage_netbird_ssl_certificates() {
    echo -e "${BLUE}Netbird SSL Certificate Management${NC}"
    echo "=================================="
    
    # Check if Netbird is installed
    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo -e "${RED}Netbird installation not found.${NC}"
        return 1
    fi
    
    echo "Netbird installation: $netbird_path"
    
    # Check current SSL setup
    local ssl_dir="$netbird_path/ssl"
    if [ -f "$ssl_dir/.custom_ssl_domain" ]; then
        local current_domain=$(cat "$ssl_dir/.custom_ssl_domain")
        echo -e "${GREEN}Custom SSL certificates currently configured for: $current_domain${NC}"
    else
        echo -e "${YELLOW}No custom SSL certificates configured. Using default Netbird certificates.${NC}"
    fi
    
    echo ""
    echo "Available actions:"
    echo "1. List available certificate domains in cloud storage"
    echo "2. Setup custom SSL certificates"
    echo "3. Remove custom SSL certificates (revert to default)"
    echo "4. Return to main menu"
    
    read -rp "Enter your choice (1-4): " choice
    
    case $choice in
        1)
            list_available_certificate_domains
            ;;
        2)
            read -rp "Enter domain name for SSL setup: " ssl_domain
            if [ -n "$ssl_domain" ]; then
                check_and_setup_ssl_certificates "$ssl_domain" "$netbird_path"
            else
                echo -e "${RED}Domain name is required.${NC}"
            fi
            ;;
        3)
            if [ -d "$ssl_dir" ]; then
                echo -e "${YELLOW}Removing custom SSL certificates...${NC}"
                sudo rm -rf "$ssl_dir"
                echo -e "${GREEN}Custom SSL certificates removed. Netbird will use default certificates.${NC}"
                echo -e "${BLUE}Note: You may need to restart Netbird services for changes to take effect.${NC}"
            else
                echo -e "${YELLOW}No custom SSL certificates found.${NC}"
            fi
            ;;
        4)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid choice.${NC}"
            ;;
    esac
}
