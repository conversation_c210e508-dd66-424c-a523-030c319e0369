#!/bin/bash

# Netbird SSL Certificate Management Functions

# Color constants for output formatting
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default rclone remote (can be overridden)
RCLONE_REMOTE="${RCLONE_REMOTE:-Eyunion}"

# Function to extract base domain from a full domain
extract_base_domain() {
    local full_domain="$1"
    
    # Remove subdomain to get base domain (e.g., netbird.example.com -> example.com)
    # This handles cases like: subdomain.domain.com -> domain.com
    echo "$full_domain" | sed -E 's/^[^.]+\.(.+)$/\1/'
}

# Function to check if rclone is available and configured
check_rclone_availability() {
    if ! command -v rclone >/dev/null 2>&1; then
        echo -e "${RED}Error: rclone is not installed or not in PATH.${NC}"
        echo "Please install rclone first to use custom SSL certificates."
        return 1
    fi
    
    # Check if the remote is configured
    if ! rclone listremotes | grep -q "^${RCLONE_REMOTE}:$"; then
        echo -e "${RED}Error: rclone remote '${RCLONE_REMOTE}' is not configured.${NC}"
        echo "Please configure your rclone remote first."
        return 1
    fi
    
    return 0
}

# Function to list available certificate domains in cloud storage
list_available_certificate_domains() {
    echo -e "${BLUE}Checking available certificate domains in cloud storage...${NC}"
    
    if ! check_rclone_availability; then
        return 1
    fi
    
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates"
    local domains=$(rclone lsd "$cert_path" 2>/dev/null | awk '{print $5}' | grep -v '^$')
    
    if [ -z "$domains" ]; then
        echo -e "${YELLOW}No certificate domains found in cloud storage.${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Available certificate domains:${NC}"
    echo "$domains" | while read -r domain; do
        echo "  - $domain"
    done
    
    return 0
}

# Function to check if certificates exist for a domain (including wildcard)
check_certificate_exists_in_cloud() {
    local domain="$1"
    local base_domain=$(extract_base_domain "$domain")
    
    if ! check_rclone_availability; then
        return 1
    fi
    
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates"
    
    # Check for exact domain match first
    if rclone lsd "$cert_path" 2>/dev/null | grep -q " $domain$"; then
        echo "$domain"
        return 0
    fi
    
    # Check for base domain (wildcard certificates)
    if [ "$domain" != "$base_domain" ] && rclone lsd "$cert_path" 2>/dev/null | grep -q " $base_domain$"; then
        echo "$base_domain"
        return 0
    fi
    
    return 1
}

# Function to validate certificate files in cloud storage
validate_certificate_files() {
    local cert_domain="$1"
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates/$cert_domain"
    
    echo -e "${BLUE}Validating certificate files for domain: $cert_domain${NC}"
    
    # Required certificate files
    local required_files=("fullchain.pem" "privkey.pem")
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if ! rclone ls "$cert_path" 2>/dev/null | grep -q " $file$"; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo -e "${RED}Missing required certificate files:${NC}"
        printf '%s\n' "${missing_files[@]}"
        return 1
    fi
    
    echo -e "${GREEN}All required certificate files found.${NC}"
    return 0
}

# Function to download certificates from cloud storage
download_certificates() {
    local cert_domain="$1"
    local target_dir="$2"
    local cert_path="${RCLONE_REMOTE}:Backups/Certificates/$cert_domain"
    
    echo -e "${BLUE}Downloading certificates for domain: $cert_domain${NC}"
    
    # Create target directory
    sudo mkdir -p "$target_dir"
    
    # Download certificate files
    echo "Downloading from: $cert_path"
    echo "Target directory: $target_dir"
    
    if rclone copy "$cert_path" "$target_dir" --progress; then
        echo -e "${GREEN}Certificates downloaded successfully.${NC}"
        
        # Set proper permissions
        sudo chmod 644 "$target_dir"/*.pem 2>/dev/null || true
        sudo chown root:root "$target_dir"/*.pem 2>/dev/null || true
        
        return 0
    else
        echo -e "${RED}Failed to download certificates.${NC}"
        return 1
    fi
}

# Function to setup SSL certificates for Netbird (pre-installation)
setup_netbird_ssl_certificates() {
    local domain="$1"
    local netbird_path="$2"
    local cert_domain="$3"

    echo -e "${BLUE}Setting up SSL certificates for Netbird...${NC}"

    # Create SSL directory in Netbird installation
    local ssl_dir="$netbird_path/ssl"
    sudo mkdir -p "$ssl_dir"

    # Download certificates to SSL directory
    if download_certificates "$cert_domain" "$ssl_dir"; then
        echo -e "${GREEN}SSL certificates downloaded successfully.${NC}"
        echo "Certificate files location: $ssl_dir"

        # Create environment variable file to prevent Let's Encrypt certificate generation
        create_ssl_override_env "$domain" "$netbird_path" "$cert_domain"

        # Create a marker file to indicate custom SSL is used
        echo "$cert_domain" | sudo tee "$ssl_dir/.custom_ssl_domain" > /dev/null

        return 0
    else
        echo -e "${RED}Failed to setup SSL certificates.${NC}"
        return 1
    fi
}

# Function to create SSL override environment variables
create_ssl_override_env() {
    local domain="$1"
    local netbird_path="$2"
    local cert_domain="$3"

    echo -e "${BLUE}Creating SSL override configuration...${NC}"

    # Create custom environment file to override SSL settings
    local ssl_env_file="$netbird_path/.ssl_override.env"

    cat << EOF | sudo tee "$ssl_env_file" > /dev/null
# Custom SSL Certificate Configuration
# This file prevents automatic Let's Encrypt certificate generation
NETBIRD_DISABLE_LETSENCRYPT=true
NETBIRD_CUSTOM_SSL=true
NETBIRD_SSL_CERT_PATH=/ssl/fullchain.pem
NETBIRD_SSL_KEY_PATH=/ssl/privkey.pem
NETBIRD_DOMAIN=$domain
NETBIRD_CERT_DOMAIN=$cert_domain
EOF

    echo -e "${GREEN}SSL override configuration created.${NC}"
}

# Function to install Netbird with custom SSL certificates (no Let's Encrypt)
install_netbird_with_custom_ssl() {
    local domain="$1"
    local install_path="$2"

    echo -e "${BLUE}Installing Netbird with custom SSL certificates...${NC}"
    echo "Domain: $domain"
    echo "Installation path: $install_path"

    # Source the SSL override configuration
    local ssl_override_file="$install_path/.ssl_override.env"
    if [ -f "$ssl_override_file" ]; then
        source "$ssl_override_file"
        echo "Using custom SSL certificates from: $NETBIRD_CERT_DOMAIN"
    else
        echo -e "${RED}SSL override configuration not found!${NC}"
        return 1
    fi

    # Download the original installation script to extract docker-compose and configs
    local temp_script="/tmp/netbird-original-install.sh"
    echo "Downloading original Netbird installation script for configuration extraction..."

    if ! curl -fsSL https://github.com/netbirdio/netbird/releases/latest/download/getting-started-with-zitadel.sh -o "$temp_script"; then
        echo -e "${RED}Failed to download original installation script.${NC}"
        return 1
    fi

    # Create a custom installation script that skips certificate generation
    local custom_script="/tmp/netbird-custom-install.sh"
    create_custom_ssl_install_script "$temp_script" "$custom_script" "$domain" "$install_path"

    # Run the custom installation script
    echo "Running custom Netbird installation..."
    chmod +x "$custom_script"

    if sudo -E bash "$custom_script"; then
        echo -e "${GREEN}Custom SSL Netbird installation completed successfully!${NC}"

        # Configure Netbird to use the custom SSL certificates
        configure_netbird_custom_ssl "$domain" "$install_path" "$NETBIRD_CERT_DOMAIN"

        # Clean up
        rm -f "$temp_script" "$custom_script"
        return 0
    else
        echo -e "${RED}Custom SSL Netbird installation failed.${NC}"
        rm -f "$temp_script" "$custom_script"
        return 1
    fi
}

# Function to create custom installation script that prevents certificate generation
create_custom_ssl_install_script() {
    local original_script="$1"
    local custom_script="$2"
    local domain="$3"
    local install_path="$4"

    echo -e "${BLUE}Creating custom installation script...${NC}"

    # Create a modified installation script
    cat << 'EOF' > "$custom_script"
#!/bin/bash

# Custom Netbird installation script with SSL certificate override
# This script prevents automatic Let's Encrypt certificate generation

set -e

NETBIRD_DOMAIN="${NETBIRD_DOMAIN}"
INSTALL_PATH="${INSTALL_PATH:-/opt/netbird}"

echo "Starting custom Netbird installation..."
echo "Domain: $NETBIRD_DOMAIN"
echo "Installation path: $INSTALL_PATH"

# Create installation directory
mkdir -p "$INSTALL_PATH"
cd "$INSTALL_PATH"

# Download docker-compose.yml and configuration files
echo "Downloading Netbird configuration files..."

# Create a basic docker-compose.yml for Netbird with custom SSL
cat << 'COMPOSE_EOF' > docker-compose.yml
version: '3.8'

services:
  # Management service
  management:
    image: netbirdio/management:latest
    restart: unless-stopped
    depends_on:
      - zdb
    volumes:
      - ./management.json:/etc/netbird/management.json:ro
      - ./machinekey:/var/lib/netbird/machinekey:ro
    environment:
      - NETBIRD_MGMT_API_PORT=80
      - NETBIRD_MGMT_GRPC_PORT=443
    networks:
      - netbird

  # Signal service
  signal:
    image: netbirdio/signal:latest
    restart: unless-stopped
    volumes:
      - ./machinekey:/var/lib/netbird/machinekey:ro
    environment:
      - NETBIRD_SIGNAL_PORT=80
    networks:
      - netbird

  # Dashboard
  dashboard:
    image: netbirdio/dashboard:latest
    restart: unless-stopped
    environment:
      - NETBIRD_MGMT_API_ENDPOINT=http://management:80
      - NETBIRD_MGMT_GRPC_API_ENDPOINT=https://${NETBIRD_DOMAIN}:443
      - NETBIRD_HOTJAR_TRACK_ID=""
      - NETBIRD_GA_TRACK_ID=""
    networks:
      - netbird

  # Caddy reverse proxy with custom SSL
  caddy:
    image: caddy:2-alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "33073:33073"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - ./ssl:/ssl:ro
      - caddy_data:/data
      - caddy_config:/config
    environment:
      - NETBIRD_DOMAIN=${NETBIRD_DOMAIN}
    networks:
      - netbird

  # Zitadel identity provider
  zitadel:
    image: ghcr.io/zitadel/zitadel:latest
    restart: unless-stopped
    command: 'start-from-init --masterkey "$(openssl rand -base64 32)" --tlsMode external'
    depends_on:
      zdb:
        condition: service_healthy
    environment:
      - ZITADEL_DATABASE_POSTGRES_HOST=zdb
      - ZITADEL_DATABASE_POSTGRES_PORT=5432
      - ZITADEL_DATABASE_POSTGRES_DATABASE=zitadel
      - ZITADEL_DATABASE_POSTGRES_USER_USERNAME=zitadel
      - ZITADEL_DATABASE_POSTGRES_USER_PASSWORD=zitadel
      - ZITADEL_DATABASE_POSTGRES_USER_SSL_MODE=disable
      - ZITADEL_DATABASE_POSTGRES_ADMIN_USERNAME=zitadel
      - ZITADEL_DATABASE_POSTGRES_ADMIN_PASSWORD=zitadel
      - ZITADEL_DATABASE_POSTGRES_ADMIN_SSL_MODE=disable
      - ZITADEL_EXTERNALDOMAIN=${NETBIRD_DOMAIN}
      - ZITADEL_EXTERNALPORT=443
      - ZITADEL_EXTERNALSECURE=true
      - ZITADEL_TLS_ENABLED=false
    networks:
      - netbird

  # PostgreSQL database for Zitadel
  zdb:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_USER=zitadel
      - POSTGRES_PASSWORD=zitadel
      - POSTGRES_DB=zitadel
    volumes:
      - zitadel_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zitadel -d zitadel"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - netbird

volumes:
  caddy_data:
  caddy_config:
  zitadel_data:

networks:
  netbird:
    driver: bridge
COMPOSE_EOF

# Create Caddyfile with custom SSL certificates
cat << CADDY_EOF > Caddyfile
# Netbird Caddyfile with custom SSL certificates
{
    auto_https off
}

${NETBIRD_DOMAIN} {
    tls /ssl/fullchain.pem /ssl/privkey.pem

    # Management API
    handle /api/* {
        reverse_proxy management:80
    }

    # Signal server
    handle /signalexchange.SignalExchange/* {
        reverse_proxy h2c://signal:80
    }

    # Zitadel
    handle /auth/* {
        reverse_proxy zitadel:8080
    }

    # Dashboard (default)
    handle /* {
        reverse_proxy dashboard:80
    }

    # Enable logging
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# Signal gRPC endpoint
${NETBIRD_DOMAIN}:33073 {
    tls /ssl/fullchain.pem /ssl/privkey.pem
    reverse_proxy h2c://signal:80
}
CADDY_EOF

# Create basic management configuration
cat << 'MGMT_EOF' > management.json
{
  "Stuns": [
    {
      "Proto": "udp",
      "URI": "stun:stun.l.google.com:19302"
    }
  ],
  "TURNConfig": {
    "Turns": [
      {
        "Proto": "udp",
        "URI": "turn:${NETBIRD_DOMAIN}:3478",
        "Username": "netbird",
        "Password": "netbird"
      }
    ]
  },
  "Signal": {
    "Proto": "https",
    "URI": "${NETBIRD_DOMAIN}:443"
  },
  "Datadir": "/var/lib/netbird",
  "HttpConfig": {
    "Address": "0.0.0.0:80"
  }
}
MGMT_EOF

# Generate machine key
openssl rand -base64 32 > machinekey

# Create environment file with credentials
cat << 'ENV_EOF' > .env
# Netbird Installation Credentials
# Generated during installation

NETBIRD_DOMAIN=${NETBIRD_DOMAIN}
ZITADEL_ADMIN_USERNAME=admin
ZITADEL_ADMIN_PASSWORD=$(openssl rand -base64 12)

# Access your Netbird installation at: https://${NETBIRD_DOMAIN}
ENV_EOF

echo "Configuration files created successfully."
echo "Starting Netbird services..."

# Start the services
docker compose up -d

echo "Waiting for services to start..."
sleep 30

echo "Netbird installation with custom SSL completed!"
echo "Access your installation at: https://${NETBIRD_DOMAIN}"

EOF

    # Set environment variables for the custom script
    sed -i "s/\${NETBIRD_DOMAIN}/$domain/g" "$custom_script"
    sed -i "s/\${INSTALL_PATH}/$install_path/g" "$custom_script"

    echo -e "${GREEN}Custom installation script created successfully.${NC}"
}

# Function to configure Netbird to use custom SSL certificates (post-installation)
configure_netbird_custom_ssl() {
    local domain="$1"
    local netbird_path="$2"
    local cert_domain="$3"

    echo -e "${BLUE}Configuring Netbird to use custom SSL certificates...${NC}"

    local ssl_dir="$netbird_path/ssl"
    local caddyfile="$netbird_path/Caddyfile"

    # Check if Caddyfile exists
    if [ ! -f "$caddyfile" ]; then
        echo -e "${RED}Caddyfile not found at $caddyfile${NC}"
        return 1
    fi

    # Backup original Caddyfile
    sudo cp "$caddyfile" "$caddyfile.backup.$(date +%Y%m%d_%H%M%S)"

    # Create new Caddyfile with custom SSL configuration
    echo -e "${BLUE}Updating Caddyfile to use custom SSL certificates...${NC}"

    # Create a new Caddyfile that uses custom certificates
    cat << EOF | sudo tee "$caddyfile" > /dev/null
# Netbird Caddyfile with custom SSL certificates
# Generated by Netbird SSL management module

$domain {
    tls /ssl/fullchain.pem /ssl/privkey.pem

    # Management API
    handle /api/* {
        reverse_proxy management:80
    }

    # Signal server
    handle /signalexchange.SignalExchange/* {
        reverse_proxy h2c://signal:80
    }

    # Dashboard
    handle /* {
        reverse_proxy dashboard:80
    }

    # Enable logging
    log {
        output file /var/log/caddy/access.log
        format json
    }
}

# Signal gRPC endpoint
$domain:33073 {
    tls /ssl/fullchain.pem /ssl/privkey.pem
    reverse_proxy h2c://signal:80
}
EOF

    echo -e "${GREEN}Caddyfile updated successfully.${NC}"

    # Update docker-compose.yml to mount SSL certificates
    local compose_file="$netbird_path/docker-compose.yml"
    if [ -f "$compose_file" ]; then
        echo -e "${BLUE}Updating docker-compose.yml to mount SSL certificates...${NC}"

        # Backup original docker-compose.yml
        sudo cp "$compose_file" "$compose_file.backup.$(date +%Y%m%d_%H%M%S)"

        # Add SSL volume mount to caddy service
        if grep -q "caddy:" "$compose_file"; then
            # Use sed to add SSL volume mount to caddy service
            sudo sed -i '/caddy:/,/^[[:space:]]*[^[:space:]]/ {
                /volumes:/a\
      - ./ssl:/ssl:ro
            }' "$compose_file"

            echo -e "${GREEN}Docker compose configuration updated.${NC}"
        else
            echo -e "${YELLOW}Warning: Could not find caddy service in docker-compose.yml${NC}"
        fi
    else
        echo -e "${YELLOW}Warning: docker-compose.yml not found${NC}"
    fi

    return 0
}

# Function to apply SSL configuration after Netbird installation
apply_pending_ssl_configuration() {
    local netbird_path="$1"

    if [ ! -f "$netbird_path/.pending_ssl_config" ]; then
        return 0  # No pending SSL configuration
    fi

    echo -e "${BLUE}Applying custom SSL certificate configuration...${NC}"

    local ssl_config=$(cat "$netbird_path/.pending_ssl_config")
    local domain=$(echo "$ssl_config" | cut -d'|' -f1)
    local cert_domain=$(echo "$ssl_config" | cut -d'|' -f2)

    echo "Domain: $domain"
    echo "Certificate domain: $cert_domain"

    # Configure Netbird to use custom SSL certificates
    if configure_netbird_custom_ssl "$domain" "$netbird_path" "$cert_domain"; then
        echo -e "${GREEN}Custom SSL configuration applied successfully.${NC}"

        # Remove pending configuration file
        sudo rm -f "$netbird_path/.pending_ssl_config"

        # Create final marker file
        echo "$cert_domain" | sudo tee "$netbird_path/ssl/.custom_ssl_domain" > /dev/null

        echo -e "${BLUE}Restarting Netbird services to apply SSL changes...${NC}"
        cd "$netbird_path"
        docker compose down
        sleep 3
        docker compose up -d

        echo -e "${GREEN}Netbird is now using your custom SSL certificates!${NC}"
        echo "Certificate domain: $cert_domain"
        echo "Target domain: $domain"

        return 0
    else
        echo -e "${RED}Failed to apply custom SSL configuration.${NC}"
        return 1
    fi
}

# Main function to check and setup SSL certificates
check_and_setup_ssl_certificates() {
    local domain="$1"
    local netbird_path="${2:-/opt/netbird}"
    
    echo -e "${BLUE}Checking for existing SSL certificates...${NC}"
    echo "Domain: $domain"
    echo "Base domain: $(extract_base_domain "$domain")"
    
    # Check if certificates exist in cloud storage
    local cert_domain
    if cert_domain=$(check_certificate_exists_in_cloud "$domain"); then
        echo -e "${GREEN}Found certificates for domain: $cert_domain${NC}"
        
        # Validate certificate files
        if validate_certificate_files "$cert_domain"; then
            # Ask user if they want to use existing certificates
            echo ""
            echo -e "${YELLOW}Found existing SSL certificates in cloud storage.${NC}"
            echo "Certificate domain: $cert_domain"
            echo "Target domain: $domain"
            
            if [ "$cert_domain" != "$domain" ]; then
                echo -e "${BLUE}Note: Using wildcard certificate from base domain.${NC}"
            fi
            
            read -rp "Would you like to use these existing certificates? (y/n): " use_existing
            if [[ "$use_existing" =~ ^[Yy]$ ]]; then
                if setup_netbird_ssl_certificates "$domain" "$netbird_path" "$cert_domain"; then
                    echo -e "${GREEN}Custom SSL certificates configured successfully.${NC}"
                    echo -e "${BLUE}Netbird installation will use these certificates and skip Let's Encrypt generation.${NC}"
                    return 0
                else
                    echo -e "${RED}Failed to setup existing certificates.${NC}"
                    return 1
                fi
            else
                echo "Skipping existing certificates. New certificates will be generated during installation."
                return 1
            fi
        else
            echo -e "${RED}Certificate validation failed.${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}No existing certificates found for domain: $domain${NC}"
        echo -e "${YELLOW}Base domain $(extract_base_domain "$domain") also not found.${NC}"
        return 1
    fi
}

# Function to manage SSL certificates (menu option)
manage_netbird_ssl_certificates() {
    echo -e "${BLUE}Netbird SSL Certificate Management${NC}"
    echo "=================================="
    
    # Check if Netbird is installed
    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo -e "${RED}Netbird installation not found.${NC}"
        return 1
    fi
    
    echo "Netbird installation: $netbird_path"
    
    # Check current SSL setup
    local ssl_dir="$netbird_path/ssl"
    if [ -f "$ssl_dir/.custom_ssl_domain" ]; then
        local current_domain=$(cat "$ssl_dir/.custom_ssl_domain")
        echo -e "${GREEN}Custom SSL certificates currently configured for: $current_domain${NC}"
    else
        echo -e "${YELLOW}No custom SSL certificates configured. Using default Netbird certificates.${NC}"
    fi
    
    echo ""
    echo "Available actions:"
    echo "1. List available certificate domains in cloud storage"
    echo "2. Setup custom SSL certificates"
    echo "3. Remove custom SSL certificates (revert to default)"
    echo "4. Test SSL certificate functionality"
    echo "5. Return to main menu"
    
    read -rp "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            list_available_certificate_domains
            ;;
        2)
            read -rp "Enter domain name for SSL setup: " ssl_domain
            if [ -n "$ssl_domain" ]; then
                check_and_setup_ssl_certificates "$ssl_domain" "$netbird_path"
            else
                echo -e "${RED}Domain name is required.${NC}"
            fi
            ;;
        3)
            if [ -d "$ssl_dir" ]; then
                echo -e "${YELLOW}Removing custom SSL certificates...${NC}"
                sudo rm -rf "$ssl_dir"
                echo -e "${GREEN}Custom SSL certificates removed. Netbird will use default certificates.${NC}"
                echo -e "${BLUE}Note: You may need to restart Netbird services for changes to take effect.${NC}"
            else
                echo -e "${YELLOW}No custom SSL certificates found.${NC}"
            fi
            ;;
        4)
            test_ssl_certificate_functionality
            ;;
        5)
            return 0
            ;;
        *)
            echo -e "${RED}Invalid choice.${NC}"
            ;;
    esac
}

# Function to test SSL certificate functionality
test_ssl_certificate_functionality() {
    echo -e "${BLUE}Testing SSL Certificate Functionality${NC}"
    echo "====================================="

    # Test rclone availability
    echo -n "Testing rclone availability: "
    if check_rclone_availability; then
        echo -e "${GREEN}✓ Available${NC}"
    else
        echo -e "${RED}✗ Not available${NC}"
        return 1
    fi

    # Test certificate listing
    echo ""
    echo "Testing certificate domain listing:"
    if list_available_certificate_domains; then
        echo -e "${GREEN}✓ Certificate listing successful${NC}"
    else
        echo -e "${YELLOW}⚠ No certificates found or listing failed${NC}"
    fi

    # Test domain extraction
    echo ""
    echo "Testing domain extraction:"
    local test_domain="netbird.example.com"
    local base_domain=$(extract_base_domain "$test_domain")
    echo "Full domain: $test_domain"
    echo "Base domain: $base_domain"

    if [ "$base_domain" = "example.com" ]; then
        echo -e "${GREEN}✓ Domain extraction working correctly${NC}"
    else
        echo -e "${RED}✗ Domain extraction failed${NC}"
    fi

    echo ""
    echo "SSL certificate functionality test completed."
    read -rp "Press Enter to continue..."
}
